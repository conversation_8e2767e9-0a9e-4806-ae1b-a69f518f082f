import EventType from "../event/EventType"
import MaskRedCmpt from "../../view/cmpt/common/MaskRedCmpt"
import ConditionObj from "../../model/common/ConditionObj"
import SpeedUpWdtCtrl from "../../view/common/SpeedUpWdtCtrl"
import { ANIMAL_TYPE_ICON, BATTLE_TYPE_ICON, CONDITION_ICON, CONDITION_ITEM_ICON_DIR, CONDITION_OTHER_ICON, CURRENY_CFG, TOOL_SLOT_NAME } from "../constant/Constant"
import { CfgName, ConditionType, ItemID, ItemType, PassengerBattleAnimation, PassengerLifeAnimation, RoleAnimalType, RoleBattleType, TrainBurstTaskType, UIFunctionType } from "../constant/Enums"
import { cfgHelper } from "./CfgHelper"
import { viewHelper } from "./ViewHelper"
import { AssetRef, BattleSkillViewCfg, CharacterSkinCfg, Condition, EquipCfg, ItemCfg, NpcCfg, PlanetCfg, PlanetMonsterCfg } from "../constant/DataType"
import { BattleSummonID } from "../../model/battle/BattleEnum"
import { gameHelper } from "./GameHelper"
import RoleTicketCmpt from "../../view/role/RoleTicketCmpt"
import { Equip } from "../../model/equip/EquipModel"

/**
 * 游戏中的资源相关帮助方法
 */
class ResHelper {

    private rolePoolMap: Map<string, cc.Node[]> = new Map()

    private prefabPoolMap: Map<string, {
        prefab: cc.Prefab,
        items: cc.Node[],
        itemUuidMap: any,
        key: string,
        refCount: number
    }> = new Map()
    private prefabUrlMap: any = {}

    private spineDefaultMaterial: cc.Material = null
    private spineLockMaterial: cc.Material = null

    private spriteDefaultMaterial: cc.Material = null
    private spriteLockMaterial: cc.Material = null

    // 预加载需要加载的资源
    public init() {
        this.rolePoolMap.clear()
        this.prefabPoolMap.clear()
        this.spineDefaultMaterial = cc.Material.getBuiltinMaterial('2d-spine')
        this.spineLockMaterial = assetsMgr.getMaterial("spine_splash")
        this.spriteDefaultMaterial = cc.Material.getBuiltinMaterial('2d-sprite')
        this.spriteLockMaterial = assetsMgr.getMaterial("spriteGray")
    }

    //常驻资源白名单，只有非常频繁创建的资源才需要
    private RES_CACHE_LIST = {}

    public async getNode(url: string, key?: string) {
        key = key || ''
        const name = url
        let pool = this.prefabPoolMap.get(name)
        if (!pool || !pool.prefab || !pool.prefab.isValid) {
            let prefab = await assetsMgr.loadTempRes(url, cc.Prefab, key)
            let currPool = this.prefabPoolMap.get(name) //防止异步回来的值已经不同了，重新取一次
            pool = { prefab, items: [], itemUuidMap: {}, key, refCount: 0 } //不用管异步之前pool的状态，如果pool == currPool，那交给下面的逻辑处理，如果不等说明已经被处理过
            if (!currPool) {
                pool.refCount++
                this.prefabPoolMap.set(name, pool)
            }
            else {
                if (!currPool.prefab || !currPool.prefab.isValid) { //当前的prefab已经失效，需要销毁全部缓存节点，用新的替换掉当前的
                    currPool.items.forEach(node => {
                        node.destroy()
                    });
                    currPool.items.length = 0

                    if (prefab.isValid && prefab.refCount > 0) {
                        pool.refCount = 1
                        this.prefabPoolMap.set(name, pool)
                    }
                    else {
                        this.prefabPoolMap.delete(name)
                        await ut.waitNextFrame()
                        return await this.getNode(url, key)
                    }
                }
                else { //如果当前的还有效，就用当前的
                    pool = currPool
                    pool.refCount++

                    //这里多计数了，需要释放
                    if (pool.refCount > 1) {
                        assetsMgr.releaseTempRes(url, key)
                    }
                }
            }
        }
        else {
            pool.refCount++
        }

        let it = pool.items.pop()
        delete pool.itemUuidMap[it?.uuid]
        if (!it || !it.isValid) {
            it = cc.instantiate(pool.prefab)
        }
        if (it) {
            it['__node_pool_url'] = name
            this.prefabUrlMap[it.uuid] = name
            // cc.log('getNode', it.name, it)
        }
        return it
    }

    public putNode(it: cc.Node) {
        if (!it || !it.isValid) {
            return
        }
        // cc.log('putNode', it.name, it)
        const name = this.prefabUrlMap[it.uuid] || it['__node_pool_url']
        if (!name) {
            it.destroy()
            return twlog.error('putNode error url == null, node=' + it.name)
        }

        //销毁掉不会频繁使用的资源
        if (!this.RES_CACHE_LIST[name]) {
            return this.destoryNode(it)
        }

        delete this.prefabUrlMap[it.uuid]
        const pool = this.prefabPoolMap.get(name)
        if (pool) {
            if (!pool.itemUuidMap[it.uuid]) {
                it.stopAllActions()
                it.parent = null
                it.Data = null
                pool.itemUuidMap[it.uuid] = true
                pool.items.push(it)
                pool.refCount--
            }
            else {
                twlog.info('putNode, repeat push item! name=' + it.name)
            }
        }
        else {
            it.destroy()
        }
    }

    //回收不常用的缓存，且销毁整个对应的缓存池
    private destoryNode(it: cc.Node) {
        if (!it || !it.isValid) {
            return
        }
        // cc.log('putNode', it.name, it)
        const name = this.prefabUrlMap[it.uuid] || it['__node_pool_url']
        if (!name) {
            it.destroy()
            return twlog.error('putNode error url == null, node=' + it.name)
        }
        delete this.prefabUrlMap[it.uuid]

        const pool = this.prefabPoolMap.get(name)
        if (pool) {
            it.destroy()

            pool.refCount--
            if (pool.refCount <= 0) {
                assetsMgr.releaseTempRes(name, pool.key)
                this.prefabPoolMap.delete(name)
            }
        }
        else {
            it.destroy()
        }
    }

    // 清理
    public cleanNodeByMod(key: string) {
        //这个清理确实是有意义的，为了最低的内存
        let keyAry = []
        this.prefabPoolMap.forEach((m, name) => {
            if (m.key === key) {
                keyAry.push(name)
            }
        })

        keyAry.forEach((name) => {
            let pool = this.prefabPoolMap.get(name)
            pool.items.forEach(node => {
                node.destroy()
            });
            pool.items.length = 0
            this.prefabPoolMap.delete(name)

            assetsMgr.releaseTempRes(name, pool.key)//删除资源
        })
    }

    public haveRefSpf(sp: cc.Sprite) {
        return sp['_ref_spf'] != null
    }
    public releaseSpriteSpf(sp: cc.Sprite) {
        if (!sp.spriteFrame) return
        let dic = sp["_ref_spf"] as AssetRef
        sp['_ref_spf'] = null
        sp.spriteFrame = null
        if (!dic) return
        assetsMgr.releaseTempRes(dic.url, dic.tag)
    }
    public async loadSpriteFrame(spr: cc.Sprite, dic: AssetRef, emptyFirst: boolean, releasePre: boolean, releaseCur: boolean) {
        let preSpr = spr.spriteFrame
        if (!cc.isValid(preSpr)) {
            preSpr = null
        }
        let preDic = spr['_ref_spf'] as AssetRef
        if (!cc.isValid(preDic)) {
            preDic = null
        }
        let preUrl = preDic?.url
        if (preSpr && preUrl) {
            if (preUrl === dic.url) return
            if (releasePre) {
                assetsMgr.releaseTempRes(preUrl, preDic?.tag)
            }
        }
        if (emptyFirst) {
            spr.spriteFrame = null
        }
        spr['_ref_spf'] = dic
        let spf = await assetsMgr.loadTempRes(dic.url, cc.SpriteFrame, dic.tag)
        if (!spf) return
        if (spr.isValid && spr['_ref_spf']?.url === dic.url) {
            spr.spriteFrame = spf
            return true
        }
        if (releaseCur) {
            assetsMgr.releaseTempRes(dic.url, dic.tag)
        }
    }
    public async loadSkeletonData(skeleton: sp.Skeleton, dic: AssetRef) {
        skeleton.skeletonData = null
        skeleton['_ref_sk'] = dic
        let skeletonData = await assetsMgr.loadTempRes(dic.url, sp.SkeletonData, dic.tag)
        if (!skeletonData) return
        if (skeleton.isValid && skeleton['_ref_sk']?.url === dic.url) {
            skeleton.skeletonData = skeletonData
            return true
        }
        assetsMgr.releaseTempRes(dic.url, dic.tag)
    }

    // 获取spine材质
    public getSpineMaterial(unlock: boolean) {
        return unlock ? this.spineDefaultMaterial : this.spineLockMaterial
    }

    // 获取sprite材质
    public getSpriteMaterial(unlock: boolean) {
        return unlock ? this.spriteDefaultMaterial : this.spriteLockMaterial
    }
    public async loadTrainHeadPrefab(id: number, parent: cc.Node, key: string) {
        let url = `train/train_head_theme_${id}`
        return this.loadPrefabByUrl(url, parent, key)
    }
    public async loadBuildPrefab(id: string, parent: cc.Node, key: string) {
        let cfg = cfgHelper.getBuildById(id)
        if (!cfg) return
        let url = `build/${cfg.prefab}`
        return this.loadPrefabByUrl(url, parent, key)
    }
    public async loadPrefabByUrl(url: string, parent: cc.Node, key: string) {
        const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, key)
        if (!pfb || !parent.isValid) {
            if (pfb) {
                assetsMgr.releaseTempRes(url, key)
            }
            return
        }
        const node = cc.instantiate2(pfb, parent)
        return node
    }

    public async setPrefab(url: string, parent: cc.Node, nodeName: string, tag: string) {
        let key = `__@${nodeName}`
        parent[key] = url
        if (!url) {
            let node: cc.Node = parent.getChildByName(nodeName)
            if (node) {
                node.removeAndDestroy()
                assetsMgr.releaseTempRes(url, tag)
            }
            return
        }
        const pfb = await assetsMgr.loadTempRes(url, cc.Prefab, tag)
        if (!pfb || !cc.isValid(parent) || parent[key] !== url) {
            if (pfb) {
                assetsMgr.releaseTempRes(url, tag)
            }
            return
        }
        let node: cc.Node = parent.getChildByName(nodeName)
        if (!node) {
            node = cc.instantiate2(pfb, parent)
            node.name = nodeName
        }
        return node
    }

    public async addPrefabLock(url: string, parent: cc.Node, key: string) {
        mc.lockTouch(true)
        let node = await this.loadPrefabByUrl(url, parent, key)
        mc.lockTouch(false)
        return node
    }
    public async addPrefabCmptLock<T extends cc.Component>(type: {
        prototype: T
    }, url: string, parent: cc.Node, key: string) {
        let cmpt = parent.getComponentInChildren(type)
        if (cmpt) return cmpt
        let node = await this.addPrefabLock(url, parent, key)
        if (node) return node.getComponent(type)
    }
    public async loadIcon(root: cc.Node, dir: string, icon: string, tag: string, setEmpty: boolean = false, adaptNodeSize: boolean = true) {
        if (!root || !dir || !icon) {
            return twlog.error("loadIcon root or dir or icon is null")
        }
        let val = root.Child('val')
        let spr
        if (val) {
            spr = root.Child('val', cc.Sprite)
        }
        else {
            spr = root.Component(cc.Sprite)
        }
        if (!spr) {
            return twlog.error("loadIcon 没有sprite组件", root.getPath())
        }
        const url = dir + '/' + icon
        let bol = await this.loadSpriteFrame(spr, { url, tag }, true, setEmpty, true)
        if (!bol || !cc.isValid(spr)) return
        if (val) {
            if (!spr.spriteFrame) return
            adaptNodeSize && viewHelper.adaptNodeSize(spr.node, spr.spriteFrame.getRect(), root.getContentSize())
        }
    }

    //注意这个接口会释放原来的图片
    public async loadTmpIcon(url: string, spr: cc.Sprite, tag: string) {
        await this.loadSpriteFrame(spr, { url, tag }, true, true, true)
    }

    public async loadFacilityIcon(sprite: cc.Sprite, facName: string, tag: string) {
        return this.loadTmpIcon(`train/itemIcon/${facName}`, sprite, tag);
    }

    public async loadPlanetIcon(id: number, sp: cc.Sprite, tag: string) {
        let data = assetsMgr.getJsonData<PlanetCfg>(CfgName.PLANET, id)
        const url = `planet/icon/${data.icon}`
        return this.loadTmpIcon(url, sp, tag)
    }

    public async loadPlanetIconHide(id: number, sp: cc.Sprite, tag: string) {
        let data = assetsMgr.getJsonData<PlanetCfg>(CfgName.PLANET, id)
        const url = `planet/icon_hide/${data.iconHide}`
        return this.loadTmpIcon(url, sp, tag)
    }

    public async loadPlanetEntryIcon(id: number, sp: cc.Sprite, tag: string) {
        let data = assetsMgr.getJsonData<PlanetCfg>(CfgName.PLANET, id)
        const url = `planet/entry/${data.entryIcon}`
        return this.loadTmpIcon(url, sp, tag)
    }

    public async loadRoleCircleIcon(id: number, sp: cc.Sprite, tag: string) {
        let cfg = cfgHelper.getCharacter(id)
        if (!cfg || !cfg.iconCircle) {
            return twlog.error('no iconCircle', id)
        }
        let url = `passenger/icon_circle/${cfg.iconCircle}`
        return this.loadTmpIcon(url, sp, tag)
    }

    public async loadRoleBigIcon(id: number, sp: cc.Sprite, tag: string) {
        let cfg = cfgHelper.getCharacter(id)
        if (!cfg || !cfg.iconBig) {
            return twlog.error('no iconBig', id)
        }
        let url = `passenger/icon_big/${cfg.iconBig}`
        return this.loadTmpIcon(url, sp, tag)
    }

    public async loadRoleHeadIcon(id: number, sp: cc.Sprite, tag: string) {
        let cfg = cfgHelper.getCharacter(id)
        if (!cfg) return
        let icon = cfg.icon
        if (!icon) return
        let url = `head/icon/${icon}`
        return this.loadTmpIcon(url, sp, tag)
    }
    public async preloadRoleSk(id: number, tag: string) {
        let skinCfg = this.getSkinCfg(id);
        let url = 'passenger/' + skinCfg.spine
        return await assetsMgr.loadTempRes(url, sp.SkeletonData, tag)
    }

    public async loadJumpRoleSkin(url: string, skeleton: sp.Skeleton | cc.Node, tag: string) {
        if (skeleton instanceof cc.Node) {
            skeleton = skeleton.Component(sp.Skeleton)
        }
        skeleton.premultipliedAlpha = false
        let bol = await this.loadSkeletonData(skeleton, { url, tag })
        if (!bol) return
        skeleton.tryPlayAnimations([PassengerLifeAnimation.IDLE, "aniIdle"], true)
        skeleton.defaultAnimation = skeleton.animation
        return skeleton.skeletonData
    }

    public async loadRoleSkin(id: number, skinIndex: number, skeleton: sp.Skeleton | cc.Node, tag: string, playOnLoad: boolean = true) {
        if (skeleton instanceof cc.Node) {
            skeleton = skeleton.Component(sp.Skeleton)
        }
        let skinCfg = this.getSkinCfg(id, skinIndex);
        if (skeleton.skeletonData && skeleton.skeletonData.name == skinCfg.spine) {
            if (skeleton.defaultSkin != skinCfg.skin) {
                this.initRoleSkin(id, skinIndex, skeleton)
            }
            return;
        }
        const url = 'passenger/' + skinCfg.spine
        let bol = await this.loadSkeletonData(skeleton, { url, tag })
        if (!bol) return

        this.initRoleSkin(id, skinIndex, skeleton)

        if (playOnLoad) {
            skeleton.tryPlayAnimations([PassengerLifeAnimation.IDLE, "aniIdle"], true)
            skeleton.defaultAnimation = skeleton.animation
        }
        return skeleton.skeletonData
    }

    public async loadRoleSp(id: number, skeleton: sp.Skeleton | cc.Node, tag: string, playOnLoad: boolean = true) {
        return this.loadRoleSkin(id, 1, skeleton, tag, playOnLoad)
    }

    public async loadOwnRoleSp(id: number, skeleton: sp.Skeleton | cc.Node, tag: string, playOnLoad: boolean = true) {
        let passenger = gameHelper.passenger.getPassenger(id)
        let skinIndex = 1
        if (passenger) {
            skinIndex = passenger.getSkin().index
        }
        return this.loadRoleSkin(id, skinIndex, skeleton, tag, playOnLoad)
    }

    public initOwnRoleSkin(id: number, sk: sp.Skeleton) {
        let passenger = gameHelper.passenger.getPassenger(id)
        let skinIndex = 1
        if (passenger) {
            skinIndex = passenger.getSkin().index
        }
        this.initRoleSkin(id, skinIndex, sk)
    }

    public initRoleSkin(id: number, skinIndex: number, sk: sp.Skeleton) {
        let cfg = this.getSkinCfg(id, skinIndex)
        let skinName = cfg.skin
        if (!sk.findSkin(skinName)) {
            skinName = "default"
        }
        sk.defaultSkin = skinName
        sk.setSkin(skinName)
    }

    public getSkinCfg(id: number, skinIndex: number = 1): CharacterSkinCfg {
        let cfg = cfgHelper.getCharacter(id);
        let skinCfg = cfg.skin
        return skinCfg.find(r => r.index == skinIndex)
    }

    public async preloadMonsterSk(id: number, tag: string) {
        let cfg = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", id)
        let url = 'monster/' + cfg.spine
        return await assetsMgr.loadTempRes(url, sp.SkeletonData, tag)
    }

    public async loadMonsterSp(id: number, skeleton: sp.Skeleton, tag: string) {
        let cfg = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", id);
        let url = 'monster/' + cfg.spine
        let bol = await this.loadSkeletonData(skeleton, { url, tag })
        if (!bol) return
        this.initMonsterSk(id, skeleton)
        return skeleton.skeletonData
    }

    public initMonsterSk(id: number, skeleton: sp.Skeleton) {
        let cfg = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", id);
        let skinName: string = cfg.skin || "skin_01"
        if (!skeleton.findSkin(skinName)) {
            skinName = "default" //如果有多个皮肤，这个为默认皮肤
        }

        skeleton.setSkin(skinName)

        skeleton.tryPlayAnimations(["aniTaunt", PassengerBattleAnimation.ATTACK_IDLE], true)
    }

    public async preloadSummonSk(id: BattleSummonID, tag: string) {
        let map = {}
        let prefix = "character"
        let baseId = BattleSummonID.BASE

        if (id > BattleSummonID.EQUIP_BASE) {
            prefix = "equip"
            baseId = BattleSummonID.EQUIP_BASE
        }
        else if (id > 12000) {
            prefix = "monster"
        }
        let spName = map[id] || `${prefix}_${id - baseId}_zhao`
        if (!spName) return
        const url = 'summon/' + spName
        return await assetsMgr.loadTempRes(url, sp.SkeletonData, tag)
    }

    public async loadMonsterCircleIcon(id: number, icon: cc.Node, tag: string) {
        let cfg = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", id)
        return this.loadIcon(icon, "monsterIcon", cfg.iconCircle, tag)
    }

    public async loadMonsterBigIcon(id: number, icon: cc.Node, tag: string) {
        let cfg = assetsMgr.getJsonData<PlanetMonsterCfg>("PlanetMonster", id)
        return this.loadIcon(icon, "monsterIcon", cfg.iconBig, tag)
    }

    private checkCondData(cond: ConditionObj, root: cc.Node) {
        let old = root.Data
        if (old && old.type == cond.type && old.id == cond.id) {
            return true
        }
        root.Data = cond
    }

    public async updatePropByCondInfo(cond: ConditionObj, root: cc.Node, key: string, scale: number = 1) {
        if (this.checkCondData(cond, root)) return
        await this.loadIconByCondInfo(cond, root, key)
        if (!cc.isValid(root)) return
        if (cond.type == ConditionType.PROP) {
            resHelper.adaptIconScale(root, 150, scale)
        }
        else {
            resHelper.adaptIconScale(root, 150, scale)
        }
    }

    public adaptIconScale(root: cc.Node, side: number, scale: number = 1) {
        let calculate = viewHelper.getAdaptScale(root.getContentSize(), cc.size(side, side))
        root.scale = calculate * scale
    }

    public async updateAssetsByCondInfo(cond: ConditionObj, root: cc.Node, key: string, size?: cc.Size) {
        if (this.checkCondData(cond, root)) return
        await this.loadIconByCondInfo(cond, root, key)
        if (!cc.isValid(root)) return
        if (size) root.scale = viewHelper.getAdaptScale(root.getContentSize(), size)
    }

    public async preloadConditions(ary: Condition[], key: string) {
        ary = ary || []
        for (const cond of ary) {
            await this.preloadOneCondition(cond, key)
        }
    }
    private async preloadOneCondition(cond: Condition, key: string) {
        const conf = CONDITION_ITEM_ICON_DIR[cond.type]
        if (conf) {
            const json = assetsMgr.getJsonData<any>(conf.jsonName, cond.id)
            if (json) {
                await assetsMgr.loadTempRes(conf.dir + '/' + json.icon, cc.SpriteFrame, key)
                return
            }
        }
    }

    public loadImageUrl(spr: cc.Sprite, url: string) {
        const sf = spr.spriteFrame = assetsMgr.getImage(url)
        spr['_ref_spf'] = null
        if (!sf) return twlog.error("loadImageUrl no sf. url:", url)
        return sf
    }

    // 加载道具icon 根据类型
    public async loadIconByCondInfo(cond: ConditionObj, root: cc.Node, key: string, setEmpty: boolean = false) {
        const orgScale = this.resetCondIcon(root)
        if (!cond) {
            return twlog.error("loadIconByCondInfo null cond")
        }
        let val = root.Child('val')
        let spr
        if (val) {
            spr = val.Component(cc.Sprite)
        }
        else {
            spr = root.Component(cc.Sprite)
        }

        let type = cond.type, id = cond.id

        if (cond.isHide) {
            return this.loadIcon(root, "prop", "yh_wenhao", key, setEmpty)
        }

        const url = CONDITION_ICON[type]
        if (url) {
            const sf = this.loadImageUrl(spr, url)
            if (sf && val) {
                viewHelper.adaptNodeSize(spr.node, sf.getRect(), root.getContentSize())
            }
            return
        }
        if (cond.type == ConditionType.FUNCTION) {
            let node = this.copyNodeByFunctionType(cond.id as UIFunctionType, root.Child('val'))
            if (!node) {
                return twlog.error("loadIconByCondInfo no node. cond:", cond)
            }
            return viewHelper.adaptNodeSize(node, node.getContentSize(), root.getContentSize())
        }
        if (cond.type == ConditionType.CHEST) {
            let itemId = cfgHelper.getChestItemId(+id)
            type = ConditionType.PROP, id = itemId
        }
        if (cond.type == ConditionType.SEED) {
            const seedCfg = cfgHelper.getSeedInfoById(cond.id as number)
            await this.loadIcon(root, "prop", seedCfg.propIcon, key, setEmpty, false)
            if (cc.isValid(root)) {
                root.Child("val").scale = orgScale
            }
            return
        }
        if (cond.type == ConditionType.PROP) {
            const json = assetsMgr.getJsonData<ItemCfg>("Item", id)
            if (json?.type == ItemType.ROLE_TICKET) {
                let node = root.Swih("RoleTicket")[0]
                if (node) {
                    node.Component(RoleTicketCmpt).init(cfgHelper.getCharacter(id as number))
                }
                else {
                    twlog.error("loadIconByCondInfo error [RoleTicket] not found:", cond)
                }
                return
            }
        }

        const arr = CONDITION_OTHER_ICON[type]
        if (arr && arr.length === 2) {
            return this.loadIcon(root, arr[0], arr[1], key, setEmpty)
        }

        const conf = CONDITION_ITEM_ICON_DIR[type]
        if (conf) {
            const json = assetsMgr.getJsonData<any>(conf.jsonName, id)
            if (!json) {
                return twlog.error("loadIconByCondInfo no json. conf:", conf)
            }
            let icon = json.icon
            if (typeof icon == "object" && icon.length) {
                if (cond.type == ConditionType.EQUIP) {
                    let level = cond.extra?.level || 1
                    level = Math.min(level - 1, icon.length - 1)
                    icon = icon[level]
                }
            }
            return this.loadIcon(root, conf.dir, icon, key, setEmpty)
        }
        twlog.error("loadIconByCondInfo error condition type:", cond)
    }

    public async switchTool(sk: sp.Skeleton, toolType: number, toolIcon: string, tag: string) {
        for (let key in TOOL_SLOT_NAME) {
            if (Number(key) != toolType) {
                sk.setSlotAttachment(TOOL_SLOT_NAME[key], null)
            }
        }
        let slotName = TOOL_SLOT_NAME[toolType]
        if (!slotName || !toolIcon) {
            return //twlog.error("switchTool error type:", toolType, toolIcon)
        }
        let info = { toolType, toolIcon }
        let key = "__@swtichTool"
        sk[key] = info
        let url = `tool/skin/${toolIcon}`
        let spf: cc.SpriteFrame = await assetsMgr.loadTempRes(url, cc.SpriteFrame, tag)
        if (!cc.isValid(spf) || !cc.isValid(sk)) {
            return assetsMgr.releaseTempRes(url, tag)
        }
        let curInfo = sk[key]
        if (curInfo.toolType != toolType && curInfo.toolIcon != toolIcon) {
            return assetsMgr.releaseTempRes(url, tag)
        }
        sk.setSlotAttachment(slotName, spf.getTexture())
    }

    public async hideTool(sk: sp.Skeleton) {
        sk["__@swtichTool"] = {}
        for (let key in TOOL_SLOT_NAME) {
            sk.setSlotAttachment(TOOL_SLOT_NAME[key], null)
        }
    }

    public async setSpf(url: string, node: cc.Node, tag: string) {
        await this.loadSpriteFrame(node.Component(cc.Sprite), { url, tag }, true, false, false)
    }

    public async loadSpeechBubble(root, tag) {
        let url = "role/speech_bubble"
        let pfb = await assetsMgr.loadTempRes(url, cc.Prefab, tag)
        if (!cc.isValid(root)) {
            assetsMgr.releaseTempRes(url, tag)
            return
        }
        let node = root.Child("speech_bubble")
        if (!node) {
            node = cc.instantiate2(pfb, root)
        }
        return node
    }

    public loadBuildIcon(node: cc.Node, cfg: any, key: string) {
        if (!cfg) {
            return this.resetBuildIcon(node)
        }
        if (cfg.icon) {
            let val = node.Swih('val')[0]
            this.loadFacilityIcon(val.Component(cc.Sprite), cfg.icon, key)
        }
        else {
            let sp = node.Swih('sp')[0]
            sp.removeAllChildren()
            this.loadBuildPrefab(cfg.id, sp, key).then((buildNode) => {
                if (!cc.isValid(node) || !buildNode) return
                buildNode.removeComponent('BuildCmpt')
                viewHelper.adaptNodeSize(buildNode, buildNode.getContentSize(), sp.getContentSize())
            })
        }
    }
    private resetBuildIcon(node: cc.Node) {
        let sp = node.Child('sp')
        let val = node.Child('val', cc.Sprite)
        sp.removeAllChildren()
        val.spriteFrame = null
    }
    private resetCondIcon(node: cc.Node) {
        let val = node.Child('val')
        if (!val) return 1
        const orgScale = val.scale
        val.scale = 1
        val.removeAllChildren()
        val.Component(cc.Sprite).spriteFrame = null
        return orgScale
    }
    public getNodeByFunctionType(type: UIFunctionType): cc.Node {
        let node = eventCenter.get(EventType.GET_UI_FUNCTION_NODE, type)
        if (!node) {
            twlog.error('getNodeByFunctionType fail', type)
        }
        return node
    }
    public copyNodeByFunctionType(type: UIFunctionType, parent: cc.Node, target?: cc.Node): cc.Node {
        target = target || this.getNodeByFunctionType(type)
        if (!target) {
            twlog.error('copyNodeByFunctionType no target', type)
            return
        }
        let node = cc.instantiate2(target, parent)
        node.active = true
        node.opacity = 255
        node.removeComponent(cc.Widget)
        node.removeComponent(cc.Button)
        node.removeComponent(cc.ButtonEx)
        node.removeComponent(MaskRedCmpt)
        node.setPosition(0, 0)
        this.doSpecailCopyNode(type, node)
        return node
    }
    private doSpecailCopyNode(type: UIFunctionType, node: cc.Node) {
        if (type == UIFunctionType.SPEED_UP) {
            node.getComponent(SpeedUpWdtCtrl).becomeIcon()
        }
    }

    public async loadPlanetBattleBg(id: number, root: cc.Node, tag: string, mapId: number = 1) {
        let url = `planet/${id}/battle_entrust_bg`
        if (mapId > 1) {
            url += `_${mapId}`
        }
        return this.loadPrefabByUrl(url, root, tag)
    }

    public async loadPlanetAreaBg(id: number, area: number, root: cc.Node, tag: string) {
        let url = `planet/${id}/bg-${area}`
        if (id == 1001 || id == 1005 || id == 1006) {
            url = `planet/${id}/deep-bg-${area}`
        }
        return this.loadPrefabByUrl(url, root, tag)
    }

    public getPlanetBattleBg(id: number, mapId: number = 1) {
        let url = `planet/${id}/battle_entrust_bg`
        if (mapId > 1) {
            url += `_${mapId}`
        }
        return url
    }

    public getBattleBg(name: string) {
        let url = `battle/bg/${name}`
        return url
    }


    public getDailyTaskBattleBg(planetId: number) {
        return `planet/${planetId}/task_battle_bg`
    }

    public async switchSlotAttachment(node: cc.Node | sp.Skeleton, slotName: string, url: string, tag: string, check: Function, slots: string[], setFunc?: Function, needRelease: boolean = true) {
        for (let slot of slots) {
            if (slot != slotName) {
                this.clearSlotAttachment(node, slot, tag)
            }
        }
        return this.updateSlotAttachment(node, slotName, url, tag, check, setFunc, needRelease)
    }

    /**
     * 用于加载并替换骨骼动画上插槽的图片
     * @param node 节点
     * @param slotName 插槽名称
     * @param url 附件url
     * @param tag 资源tag
     * @param check 校验函数, 返回true加载附件，返回false不加载，如果已加载则清理
     * @param setFunc 设置函数, 用于自定义设置slot附件
     * @param needRelease 是否只保留当前的，释放其他的
     * @param clearFilter 清理过滤函数,用于自定义哪些附件需要清理
     * 因为同一个插槽只能同时存在一个附件，如果两个功能共用一个插槽，清理时可能存在冲突，需要通过这个自定义方法决定哪个功能需要清理哪个url
     */
    public async updateSlotAttachment(node: cc.Node | sp.Skeleton, slotName: string, url: string, tag: string, check: Function, setFunc?: Function, needRelease: boolean = true, clearFilter?: (url: any) => boolean) {
        let sk
        if (node instanceof cc.Node) {
            sk = node.Component(sp.Skeleton)
        }
        else {
            sk = node
        }

        if (!check()) { //如果不通过校验，清理
            this.clearSlotAttachment(node, slotName, tag, clearFilter)
        }
        else {
            if (!sk.findSlot(slotName)) {
                twlog.error("updateSlotAttachment no slot:", slotName, url)
                return
            }

            let slotMap = sk["__slotMap"]
            if (!slotMap) sk["__slotMap"] = slotMap = {}
            if (!slotMap[slotName]) slotMap[slotName] = { attachments: [], cur: null }

            let attachments = slotMap[slotName].attachments
            let attachment = attachments.find((info) => info.url == url)
            if (!attachment) {
                attachment = { url, spf: null }
                attachments.push(attachment)
            }
            slotMap[slotName].cur = attachment

            let spf = attachment?.spf
            if (!spf) {
                spf = await assetsMgr.loadTempRes(url, cc.SpriteFrame, tag)
                if (!cc.isValid(spf) || !cc.isValid(sk)) {
                    assetsMgr.releaseTempRes(url, tag)
                    return
                }
                let cur = slotMap[slotName].cur
                if (cur != attachment) {
                    assetsMgr.releaseTempRes(url, tag)
                    attachments.remove(attachment)
                    return
                }
                if (!check()) {
                    this.clearSlotAttachment(node, slotName, tag, clearFilter)
                    return
                }

                //先释放之前的
                if (needRelease) {
                    for (let i = attachments.length - 1; i >= 0; i--) {
                        let attachment = attachments[i]
                        if (cur == attachment) continue
                        if (!clearFilter || clearFilter(attachment)) {
                            assetsMgr.releaseTempRes(attachment.url, tag)
                            attachments.splice(i, 1)
                        }
                    }
                }
                attachment.spf = spf
            }
            if (setFunc) {
                setFunc(spf, slotName)
            }
            else {
                sk.setSlotAttachment(slotName, spf.getTexture())
            }
        }
    }

    public clearSlotAttachment(node: cc.Node | sp.Skeleton, slotName: string, tag: string, filter?: (attachment: any) => boolean) {
        let sk
        if (node instanceof cc.Node) {
            sk = node.Component(sp.Skeleton)
        }
        else {
            sk = node
        }
        let slotMap = sk["__slotMap"]
        if (!slotMap) return
        if (!slotMap[slotName]) return
        let attachments: { spf, url }[] = slotMap[slotName].attachments
        if (!(attachments?.length)) return
        let cur = slotMap[slotName].cur

        let clearCur = false
        for (let i = attachments.length - 1; i >= 0; i--) {
            let attachment = attachments[i]
            if (!filter || filter(attachment)) {
                if (cur == attachment && cur.spf) {
                    clearCur = true
                }
                assetsMgr.releaseTempRes(attachment.url, tag)
                attachments.splice(i, 1)
            }
        }

        if (clearCur) {
            sk.setSlotAttachment(slotName, null)
            slotMap[slotName].cur = null
        }
    }

    public getCfgByCond(cond: ConditionObj | Condition) {
        let cfg: { name: string, content: string }
        let type = cond.type
        if (type == ConditionType.PROP) {
            cfg = cfgHelper.getPropData(cond.id as number)
        }
        else if (type == ConditionType.BUILD_ID) {
            cfg = cfgHelper.getBuildById(cond.id as string)
        }
        else if (type == ConditionType.CHEST) {
            let itemId = cfgHelper.getChestItemId(+cond.id)
            cfg = cfgHelper.getPropData(itemId)
        }
        else if (type == ConditionType.EQUIP) {
            cfg = assetsMgr.getJsonData('Equip', cond.id)
        }
        else if (type == ConditionType.ORE_ITEM) {
            cfg = assetsMgr.getJsonData('OreItem', cond.id)
        } else if (type == ConditionType.CHARACTER_FRAG) {
            cfg = assetsMgr.getJsonData('CharacterFrag', cond.id)
        }
        else {
            cfg = CURRENY_CFG[type]
        }
        if (!cfg) {
            cc.error('getCfgByCond no cfg:', type, cond)
        }
        return cfg
    }

    public getDescByCond(cond: ConditionObj | Condition) {
        let cfg: any = this.getCfgByCond(cond)
        let role = cfgHelper.getCharacter(+cond.id)
        let name, content
        if (typeof cfg.name == "string") {
            name = assetsMgr.lang(cfg.name)
        }
        if (typeof cfg.content == "string") {
            content = assetsMgr.lang(cfg.content)
        }
        if (cond.type == ConditionType.PROP && !!role) { //角色碎片
            name = assetsMgr.lang(cfg.name, assetsMgr.lang(role.name))
            content = assetsMgr.lang(cfg.content, assetsMgr.lang(role.name))
        }
        else if (cond.type == ConditionType.EQUIP) {
            let equip = new Equip().init({ id: Number(cond.id), level: cond.extra?.level || 1 })
            name = assetsMgr.lang(equip.name)
            content = ""
        } else if (cond.type == ConditionType.CHARACTER_FRAG) {
            role = cfgHelper.getCharacter(cfg.characterId)
            name = assetsMgr.lang(cfg.name, assetsMgr.lang(role.name))
            content = assetsMgr.lang(cfg.content, assetsMgr.lang(role.name))
        }
        if (cond.isHide) {
            name = "? ? ?"
            content = ""
        }
        return { name, content }
    }

    public getScaleByCond(cond: ConditionObj | Condition) {
        if (cond.type == ConditionType.EQUIP) {
            return 0.7
        }
        return 1
    }

    public loadBattleTypeIcon(type: RoleBattleType, icon: cc.Node, tag) {
        let url = `passenger/battle_type/${BATTLE_TYPE_ICON[type]}`
        return this.loadTmpIcon(url, icon.Component(cc.Sprite), tag);
    }

    public loadAnimalTypeIcon(type: RoleAnimalType, icon: cc.Node, tag) {
        let url = `passenger/animal_type/${ANIMAL_TYPE_ICON[type]}`
        return this.loadTmpIcon(url, icon.Component(cc.Sprite), tag);
    }

    public loadSkillIcon(skill, iconNode: cc.Node, tag) {
        let url = `skill/icon/${skill.icon}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag);
    }

    public loadBlackHoleEquipIcon(equip, iconNode: cc.Node, tag) {
        let url = `blackHole/equip/${equip.icon}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag);
    }

    public loadPlanetReachBg(id: number, iconNode: cc.Node, tag) {
        let url = `planet/reach/${id}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag);
    }

    public async loadPlanetReachSk(id: number, iconNode: cc.Node, tag) {
        let url = `planet/reach/xqdd_${id}`
        return await this.loadSkeletonData(iconNode.Component(sp.Skeleton), { url, tag });
    }

    public loadTalentIcon(roleId: number, index: number, iconNode: cc.Node, tag) {
        let url = `talent/talent_icon_${roleId}_${index}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag);
    }

    public loadTalentIconBySrc(src: string, iconNode: cc.Node, tag) {
        return this.loadTmpIcon(src, iconNode.Component(cc.Sprite), tag);
    }

    public loadEquipIcon(id: number, level: number, iconNode: cc.Node, tag) {
        let tmpEquip = new Equip().init({ id, level })
        let url = `equip/${tmpEquip.icon}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag)
    }

    public loadSceneEquipIcon(id: number, level: number, iconNode: cc.Node, tag) {
        let tmpEquip = new Equip().init({ id, level })
        let url = `equip/scene/cj_${tmpEquip.icon}`
        return this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag)
    }

    public loadEquipQualityIcon(quality: number, iconNode: cc.Node, tag) {
        let url = `equip/quality/wk_dzj_pz_${quality}_1`
        this.loadTmpIcon(url, iconNode.Component(cc.Sprite), tag)
    }

    public async loadNpcSp(id: number, node: cc.Node, tag: string) {
        let cfg = assetsMgr.getJsonData<NpcCfg>("Npc", id)
        let url = `npc/${cfg.spine}`
        let sk = node.Component(sp.Skeleton)
        await this.loadSkeletonData(sk, { url, tag })
    }

    public async loadToolIcon(icon: string, node: cc.Node, tag: string) {
        let url = `tool/icon/${icon}`
        return this.loadTmpIcon(url, node.Component(cc.Sprite), tag)
    }

    public async loadBurstEffect(type: TrainBurstTaskType, node: cc.Node, tag: string): Promise<cc.Node> {
        let map = {
            [TrainBurstTaskType.FIRE]: "fire",
            [TrainBurstTaskType.POWER]: "power",
            [TrainBurstTaskType.METEOR]: "meteor",
        }
        let name = map[type]
        let url = `burst/effect/${name}`
        return this.setPrefab(url, node, name, tag)
    }
    public async loadZjmIconByCondInfo(cond: Condition | ConditionObj, node: cc.Node | cc.Sprite, tag: string) {
        if (!cond) return twlog.error("loadZjmIconByCondInfo null cond")
        cond = cond instanceof ConditionObj ? cond : new ConditionObj().init(cond)
        const type = cond.type
        const id = cond.id as number
        let name = ""
        switch (true) {
            case cond.type == ConditionType.DIAMOND:
                name = "zjm_jiemianyong_2"
                break
            case cond.type == ConditionType.STAR_DUST:
                name = "zjm_jiemianyong_0"
                break
            case cond.type == ConditionType.HEART:
                name = "zjm_jiemianyong_1"
                break
            case cond.type == ConditionType.BLACK_HOLE_CURRENCY:
                name = "zjm_jiemianyong_19"
                break
            case cond.type == ConditionType.PROP:
                const propCfg = cfgHelper.getPropData(id)
                if (propCfg) {
                    name = propCfg.zjm
                }
                break
        }
        if (!name) return twlog.error("loadZjmIconByCondInfo no url:", cond)
        const url = `zjm/${name}`
        const sp = node instanceof cc.Node ? node.Component(cc.Sprite) : node
        return await this.loadTmpIcon(url, sp, tag)
    }

}

export const resHelper = new ResHelper()
if (cc.sys.isBrowser) {
    window['resHelper'] = resHelper
}
