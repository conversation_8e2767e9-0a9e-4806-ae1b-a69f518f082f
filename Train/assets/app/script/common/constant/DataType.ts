import {
    BattleSkillCampType,
    BattleSkillEffectType,
    BattleSkillObjectType,
    BattleSkillTriggerType
} from '../../model/battle/BattleEnum';
import {
    BedState,
    ConditionType,
    ExtractType,
    GuideStepType,
    ItemType,
    MapType,
    ValueType,
    LangCfgName,
    UIFunctionType,
    GuideFingerType,
    PassengerAttr,
    GuideStepMark,
    CarriageUsePosType,
    RoleDir,
    WeakGuideType,
    GuideFingerDir,
    TaskType,
    RoleBattleType,
    RoleAnimalType,
    LifeSkillEffectType,
    GoodsObjectType,
    InstanceWeather,
    EquipEffectType,
    EquipEffectTarget,
    IntType,
    GuiderType
} from "./Enums";

type ItemInfo = {
    id: number,
    lv: number,
    type: ItemType,
    count: number,
}

type MapJsonItem = {
    id: number;
    type: MapType;
    origin: Point;
    w: number;
    h: number;
    walls: Point[];
    walls2: { point: string, size: string }[];
    positions?: Point[];
    spawn?: Point[];
    door?: { id: string, point: string };
    outgr?: { point: string, size: string };
}

type DebugInfo = {
    label: string,
    func: Function,
}

type AssetRef = {
    url: string
    tag: string
}

type BedData = {
    id: number,
    state: BedState,
}

type XYData = {
    x: number
    y: number
}

type CurMaxData = {
    cur: number
    max: number
}

type MinMaxData = {
    min: number
    max: number
}

type Condition = {
    type: ConditionType
    id?: number | string
    num: number
    isHide?: boolean,
    extra?: any
}

type TaskTarget = {
    type?: ConditionType
    id?: any
    num?: number
}

type UnlockFuncTarget = {
    type: ConditionType
    id?: any
    num?: number
}

type Trigger = {
    type: string
    id: any
}

type TypeIds = {
    type: number
    id: any[]
}

type DosingCondition = Condition & {
    hide: boolean
}

type Reward = Condition & {
    weight?: number
}

// 道具配置
type ItemCfg = {
    id: number
    type: number
    level: number
    sortId: number
    icon: string
    name: string
    content: string
    isShow?: number
    isTrans?: number
    isKey?: number
    isUse?: number
    isUnique?: number
    planetUse?: {
        scale: number
        rotate: number
    },
    message?: string
    zjm?: string
}

// 宝箱配置
type ChestCfg = {
    id: number
    itemId: number
    reward: {
        type: ConditionType
        id?: number
        num?: number
        rate?: number
    }[]
}

// 设施配置
type BuildCfg = {
    id: string;
    skin: number //主题序号
    order: number //设施序号
    carriageId: number //所属车厢id
    name: string; //设施名字
    icon: string; //图标
    type?: number;//设施类型
    content: string; //详情文案
    prefab: string; //预制体名字
    unlockType: number; //解锁方式 0:默认解锁 1:需修复 2:需购买
    show: number; //是否展示在购买列表
    sortId: number;
    preId?: string[];//需先建设
    point: number[]; //产出点位
    hideOrder: number[]; //播放设施修建动画时需要隐藏的设施序号列表
}

// 设施等级配置
type BuildLevelCfg = {
    id: string;
    order: number //设施序号
    carriageId: number //所属车厢id
    level: number
    buyCost: Condition[]; //购买消耗
    add: { [key: string]: number } //加成
}

type CarriageThemeCfg = {
    id: string,
    carriageId: number, //所属车厢
    order: number //主题序号(等级)
    name: string, //主题名字
    img: string, //主题图片
    beforeOrder?: number //前置主题序号
    roleCnt?: number //增加人口数
    workCnt?: number
    unlockLevel: number //当前主题设施最大等级
    buyCost: Condition[] //购买消耗
    add: { star: number, heart: number, electricity: number, water: number }
    collect?: number //解锁一键收取
    goodsCnt?: number //上架新货物
}

type CharacterCfg = {
    id: number;
    type: number,
    quality: number,
    suitcaseScale: number//行李箱缩放系数
    notOpen: boolean
    copy: number
    name: string
    content: string
    icon: string
    iconCircle: string
    iconBig: string
    profile: {
        age: string
        sign: string
        job: string
        personality: string
        intrest: string[]
        uninterest: string
        nasty: string
        petPhrase: string
        friends: number[]
        link: string
    },
    skillTmpId?: number
    battleSkill?: number
    lifeSkill: number[]
    likes?: number[]
    hates?: number[]
    event?: any[],
    starNum: number
    letter?: number[]
    skin: CharacterSkinCfg[]
    workAt: number[]
    sortId: number
    battleType: RoleBattleType
    animalType: RoleAnimalType

    attack: number
    hp: number

    battle?: {
        startPosOffset?: number
        width?: number
    },
    weight?: number
    isBoss?: boolean
}

//星级配置
type CharacterStarLvCfg = {
    id: number,
    lv: number,
    quality: number,
    levelMax: number,
    upCost: number;
    add: {
        star?: number,
        attack?: number,
        hp?: number,
        battleSkill?: number,
        battleSkillPassive?: number,
        lifeSkill?: number,
        explorationSkill?: number
    }
}

type CharacterLevelCostCfg = {
    id: number
    buyCost: Condition[]
}

/**角色天赋配置 */
type CharacterTalentCfg = {
    type: PassengerAttr,
    index: number
    id: string,
    icon: string,
    name: string,
    content: string,
    object: TypeIds,
    effect: { type: number, valueType: ValueType }
    baseeffect: { baseValue: number }
}

type ToolTabelCfg = {
    id: number,
    maxToolLv: number
    part: string[]
    buyCost: { type: number, id: number, num: number }[]
}

type ToolCfg = {
    id: number
    type: number
    icon: string
    skin: string
    name: string
    anim: number,
}

type ToolLevelCfg = {
    id: string
    toolId: number
    quality: number
    attack: number
    amp: number
    break: number
    hit: number
    buyCost: { type: number, id: number, num: number }[]
}

type ExploreBoxCfg = {
    id: number
    box: number[]
}

type EntrustLifeAttribute = {
    explore: number
    heart: number
    tip: number
    attack: number
    hp: number
}

type EntrustLifeCfg = {
    id: string
    weight: number
    roll: {
        a: number
        b: number
        c: number
    }
    target: {
        people: number,
        time: number
    },
    type: string
    name: string
    content: string
    request: string
    attribute: string
    bg: string
}

type BaseSkillCfg = {
    id: string,
    icon: string,
    name: string,
    content: string
    ESC?: {
        type?: string
        value: number | string
    }[]
}

type SkillEffectObject = {
    effective: {
        type: string
        id: number[]
    }[]
    object: {
        type: string
        id: number[]
    }[]
    effect: {
        type: string
        value: number
        valueType: ValueType,
    }[]
}

type BattleSkillCfg = BaseSkillCfg & {
    contentValue: (number | string)[],
    trigger: BattleSkillTriggerCfg,
    object: BattleSkillObjectCfg[],
    effect: BattleSkillEffectCfg[],
    times?: number,
    cd?: number
    roundTimes?: number
}

type BattleSkillTriggerCfg = {
    type: BattleSkillTriggerType,
    object: BattleSkillObjectType,
    camp?: BattleSkillCampType,
    count?: number
}

type BattleSkillObjectCfg = {
    type: BattleSkillObjectType,
    camp?: BattleSkillCampType,
    count?: number
}

type BattleSkillEffectCfg = {
    type: BattleSkillEffectType,
    valueType: ValueType,
    value: number,
    times?: number,
    repeats?: number
    cd?: number
    buff?: BattleSkillEffectBuffCfg,
    roleTimes?: number,
    roundTimes?: number,
}

type BattleSkillEffectBuffCfg = {
    activeObject: BattleSkillObjectType,
    rounds: number,
    delayRounds: number,
    dep: BattleSkillEffectType,
    times: number,
    repeats: number,
}

type BattleSkillViewCfg = BaseSkillCfg & {
    target: number
    triggerContent: string,
    content: string,
    content2: string,
    extra: string,
    extraContent2: boolean

    load?: number
    ready?: {
        func?: string
        isGainBySelf?: boolean
    }
    show?: {
        func: string
    },
    skill?: {
        func: string
        speed?: number
        skin: string
        delay: number,
        hitEffectFunc: string,
    },
    deathSkillByDiaup?: boolean
    addBuff: {
        func: string
    }
    removeBuff: {
        func: string
        delay: boolean
    }
    buffMountPoint: string,
}

type BattleSkillPassiveCfg = BaseSkillCfg & SkillEffectObject

type CharacterLifeSkillCfg = BaseSkillCfg & SkillEffectObject & {
    request?: string
}

type CharacterExplorationSkillCfg = BaseSkillCfg & {
    object?: {
        type: string
    }[]
    effect: {
        type: string
        value: number
    }[]
}

type CharacterSkinCfg = {
    id: string;
    characterId: string;
    index: number
    png: string;
    spine: string;
    skin: string;
    face: CharacterFaceCfg[];
    effect: {
        type: string
        value: number
        valueType: ValueType,
    }[];
    buyCost: Condition[];
}

type CharacterFaceCfg = {
    id: string;
    skin: string;
    faceCatrgory: number;
    faceImg: string;
}

type CharacterFragCfg = {
    id: string;
    characterId: string;
    quality: number;
    name: string;
    content: string;
    icon: string;
}

type GalaxyCfg = {
    id: number,
    name: string,
    lock?: {
        galaxyId: number
    }
}

type PlanetCfg = {
    id: number,
    lock?: {
        type: number,
        id: string,
    }
    name: string,
    content: string,
    games: string[],
    entryName: string,
    icon: string,
    iconHide: string,
    entryIcon: string,
    windName: string,
    galaxy: number
    explore: {
        time: number
    }
    profileUnlock: Condition[]
}

type PlanetMineCfg = {
    id: number
    type: number
    name: string
    icon: string
    reachOffset: XYData
    centerOffset: XYData
    planetId: number[]
    collectEffect: string
}

type ChapterPlanetMineCfg = {
    id: string
    lv: number
    gameType?: string
    type: number
    qteId?: number
    hp: number
    tenacity: number
    defense: number
    dodge: number
    restore: number
    reward: (Condition & { iconNum?: number })[]
    planetId: number,
    mineId: number,
}

type PlanetMonsterCfg = {
    id: number
    quality: number
    score: number
    copy: number
    name: string
    iconBig: string
    iconCircle: string
    spine: string
    skin?: string
    battleSkill?: number
    reachOffset: XYData
    isBoss: boolean
}

type PlanetMonsterLevelCfg = {
    id: string
    attack: number
    hp: number
    battleSkill: { level: number }
}

type ChapterPlanetMonsterCfg = {
    id: string
    type: string
    plotKey?: string
    sceneType?: number
    battleScene?: {
        mapId: number
        x: number
    }
    iconId?: number
    reward: Condition[]
    monster: {
        id: number
        lv: number
        starLv: number
    }[]
    hero?: number[]
}

type ChapterPlanetSpCfg = {
    id: string
    lv: number
    hp: number
    time: number
    count: number
    class: string
}

type ChapterControlCfg = {
    before?: {
        index: number
        type?: number
    }
}

type TrainCfg = {
    id: number
    sortId: number
    type: number
    isWork?: number
    title: string
    name: string
    content: string
    preTrain?: number[]
    load: number
    needWater?: number
    needElectric?: number
    isOpen: number
    cap: number
    roleCnt: number
    workCnt: number
    costTime: number
    buyCost: Condition[]
}

type TrainWorkCfg = {
    id: string;
    train: number,
    index: number,
    content: string;
    unlock?: TypeIds[],
    buyCost?: Condition[];
}

type TrainGoodsCfg = {
    id: string;
    trainId: number,
    type: number,
    sortId: number,
    name: string;
    icon: string,
    drop: string,
    iconScale: number,
    goodsCnt: number,
    foodItem?: string[],
    handsFood?: boolean,
    drinkAnim?: string,
    object?: {
        type: GoodsObjectType,
        id: number,
    }[],
}

type TrainGoodsLevelEffect = {
    type: LifeSkillEffectType
    value: number
    valueType: ValueType
}

type TrainGoodsLevelCfg = {
    id: string
    lv: number
    goodsId: string
    buyCost?: DosingCondition[]
    attrValue: number
}

type ArrestCfg = {
    id: number
    content: string
}

type IdTargets = {
    id: string
    target: TaskTarget[]
}

type TaskCfg = IdTargets & {
    type: TaskType
    preTask: string
    trigger: Trigger
    priority: number
    simple: string
    content: string
    reward?: Condition[]
    mark?: string
}

type AchievementCfg = IdTargets & {
    lv: number
    icon: string
    typeId: number
    reward: Condition[]
}

type AchievementTypeCfg = {
    id: number
    type: TaskType
    name: string
    simple: string
    trigger: Trigger
    priority: number
}

// 地图上面掉落区域
type MoneyAreaInfo = {
    id: any;
    pos: cc.Vec2
    count: number;
    tipCount?: number;
    up?: boolean; //在空中
    tips?: boolean; //小费
    offsetX: number;
    tag: string;
}

type AreaInfo = {
    id?: any;
    rect: cc.Rect;
}

type MessageBoxOpts = {
    ok?: string,
    cancel?: string,
    // mask?: boolean, //是否显示mask
    lockClose?: boolean, //禁止点击空白关闭
    lang?: LangCfgName, //使用的语言表
    title?: string,
    mask?: boolean
}

type TipsOpts = {
    ok?: Function,
    cancel?: Function,
    showAnim?: boolean,
}

type GuideStep = {
    id: number,
    unlockFunc?: UIFunctionType,
    plotKey?: string,
    priority: number,
    skip: number,
    offset?: {
        x: number,
        y: number,
        location: string,
    },
    guide?: {
        offset: XYData
        type: GuideFingerType,
        location: string,
        location_drag: string, //用于引导拖动
        direction: GuideFingerDir, //手指方向
    },
    delayTime?: number,
    goto: number,
    next: number,
    advancedAllow?: boolean, //允许在引导出现前点击
    mask?: boolean, //是否要遮罩
    gameTime?: {
        hour: number,
        min: number,
    },
    ignoreClose?: string,//不需要关闭的界面
    logic: {
        //先检测
        check?: string,//会被频繁调用 最好能有唯一性(可避免和其他新手同时争抢)
        checkEvent?: string,
        //再调用
        callReset?: string,//在call之前重置一些数据 以防数据紊乱造成引导卡死
        call?: string,
        callArgs?: any[],
        //等待调用成功
        wait?: string,
        waitArgs?: any[],
        //此时这个新手步骤宣告结束
        //等待动画结束
        waitAct?: string,
        waitActArgs?: any[],
        //下一步
    },
    mark: GuideStepMark
    guider: {
        offset: XYData
        type: GuiderType,
    }
}

type FingerGuide = {
    type: GuideFingerType
    scale?: number
    offset?: XYData
    direction?: GuideFingerDir //手指方向
}

type WeakGuideCfg = {
    id: WeakGuideType
    delay?: number
    check: {
        func: string
    },
    guide: FingerGuide,
}

type Guide = {
    id: number;  // 教程模块id
    force: boolean; //是否强制教程
    steps: GuideStep[];
}

type PlotStep = {
    id: string,
    order: number,
    next?: number,
    key: string,
    npcId: string | number
    anim?: string
    balloons?: string,
    location?: string
    call?: string,
}

type DialogOption = {
    name: string,
    nameFunc: Function,
    func: Function,
}

type CharacterPlotStep = {
    id: string
    key: string
    type: number
    order: number
    next?: number
    npcId: string | number
    call?: string
    location?: string
    reward?: Condition[]
    buyCost?: Condition[]
}

type CharacterPlotControl = {
    id: string
    preId?: string
    order: number
    plotKey: string
    trigger: Condition[]
    characterId: number
}

type CharacterProfileCfg = {
    id: number
    type: number
    age: number
    sign: string
    characterId: number
    content: string
    icon: string
    story: string
    link: string
}

type CharacterProfileAttrCfg = {
    id: string
    unlockCount: number
    effect: {
        type: number
        target: number
        val: number
        isPercent: number
    }[]
}

type PlanetProfileCfg = {
    id: number
    type: number
    sort: number
    planetId: number
    area: number
    content: string
    icon: string
    name: string
    weight: number
    target?: UnlockFuncTarget[]
}

type UseBuildCfg = {
    id: string,
    weight: number,
    index: number,
    time?: number[] | number, //时间
    times?: number[] | number, //次数
    satisfy?: number,
    depId?: string,
    des?: string,
    doNow?: boolean
}

type BuildUseInfo = {
    id: string,
    index: number,
    data?: any
}

/**抽卡历史记录 */
type ExtractHistory = {
    time: number,
    type: ExtractType,
    prize: Array<string>
}

type SpineAnimCfg = {
    id: string,
    animations: SpineAnimData[],
}

type SpineAnimData = {
    name: string,
    duration: number,
    events: { name: string, time: number }[]
}

type CarriageUsePosInfo = {
    id?: string,
    type?: CarriageUsePosType,
    dir?: RoleDir,
    force?: boolean
    pos: cc.Vec2,
    index: number,
    rect?: cc.Rect
    buildOrder?: number
}

//车厢累计信息
type AccCarriageInfo = {
    roles: number[] //使用过的角色id,
    useNum: number //累计使用次数/使用时间（单位小时）
}

type GoGameCfg = {
    id: string
    round: number
    coord: XYData
}

type QteGamePlayCfg = {
    id: string
    succeed: number
    lumb: {
        type: string
        angle: number
        floatAngle: number
    }
    pointer: {
        start: number
        floatStart: number
        acc: number
        floatAcc: number
        speed: number
        floatSpeed: number
    }
    noFail?: number
}

type JumpTipJump = {
    type: UIFunctionType
    icon: number
    name: string
    content: string
}

type JumpTipsCfg = {
    id: number
    title: string
    priority: number
    jump: JumpTipJump[]
    cond: Condition
}

type JackpotCfg = {
    id: number
    type: number
    weight: number
    enter: {
        type: number
    }
}

type TypeIdNumStyle = Condition & { style?: number }
type ShopCfg = {
    id: string
    productId: string
    type: number
    sortId: number
    icon: string
    product: Condition[]
    first: Condition[]
    gifts: Condition[]
    rmb: number
}

type FocusCfg = {
    needFocusZoomRatio: boolean     //focus时视距是否拉近
    needBackZoomRatio: boolean      //focus结束视距是否拉回原位/拉回到backZoomRatio
    needBackX: boolean              //focus结束x是否拉回原位
    time: number                    //focus时间
    offX?: number                   //focus后x偏移
    backZoomRatio: number
}

type BlackHoleLayerCfg = {
    id: number
    layer: number,
    count: number,
    box_icon?: number
}

type TowerMonsterCfg = {
    id: string
    iconId?: number
    monster: {
        id: number
        lv: number
        starLv?: number
    }[]
    reward: Condition[]
}

type UnlockFuncCfg = {
    id: number
    type: UIFunctionType
    name?: string
    icon?: string
    isShow?: number
    target?: UnlockFuncTarget[]
    lockTip?: string
    showConditions?: UnlockFuncTarget[]
}

type EquipCfg = {
    id: number
    name: string
    icon: string[] // 等级 index
    qualityIcon: string
    content: string
    sortId: number
    skill: number
    roleId: number
    index: number
}

type EquipLevelCfg = {
    id: number
    quality: number
    effect: {
        id: number,
        min: number,
        max: number
        store: number,
    }[]
    money: number
}

type EquipEffectCfg = {
    id: number,
    type: EquipEffectType,
    target: EquipEffectTarget,
    equipIndex: number
}

type TalentAttrCfg = {
    id: number,
    type: EquipEffectType,
    target: EquipEffectTarget,
}

type EquipStoreCfg = {
    id: number
    name: string
    planetId: number
    level: number
    equipIndex: number
    cost: Condition[]
}

type InstanceCfg = {
    id: number
    name: string
    close: string
    content: string
    /**周一到周日:0-6*/
    day: number[]
}

type InstanceLevelCfg = {
    id: number
    iconId: number
    weather: InstanceWeather
    monster: {
        id: number
        lv: number
        starLv?: number
    }[]
    reward: Condition[]
}

type WantedCfg = {
    costTime: number
    starCondition: number
    addPeople: number
}

type FieldSeedCfg = {
    id: number
    name: string
    icon: string
    seedIcon: string
    treeIcon: string
    fruitIcon: string
    type: number
    time: number
    price: Condition
    waterCost: Condition
    reward: Condition[]
    propIcon: string
    content: string
}

type TransportLevelUpCfg = {
    id: number
    starLv: { min: number, max: number }
    num: number
    target: {
        type: string, id: number, num: number
    }[]
}

type FieldLevelCfg = {
    id: number
    vegetable: number
    fruit: number
    seedId: number[]
    target: {
        type: string, id: number, num: number
    }[]
}

type OreItemCfg = {
    id: number
    name: string
    content: string
    icon: string
    xp: number
}

type EquipMakeCfg = {
    id: string
    level: number
    proficiency: number
    cost: Condition[]
    times: number
}

type OreLevel = {
    id: number
    iconId: number // 怪物使用的icon
    monster: BattleRole[]
    reward: Condition[]
}

type CollectMapCfg = {
    id: number
    ceil: {
        type: number
        icon: number
    }[]
}

type PlanetAreaCfg = {
    id: string
    planetId: number
    index: number
    maps: number[]
    name: string
    content: string
    windName: string
    battleBg: string
    role?: string
}

type BattleRole = {
    id: number
    lv: number
    starLv?: number
}

type TransportLevelCfg = {
    id: number
    load: number
    exp: number
    monster: BattleRole[]
}

type DailyTaskCfg = {
    id: number
    type: number
    contents: string[],
}

type SpaceStoneCfg = {
    id: number,
    markCnt: number,
    energy: number,
    buyCost: Condition[]
}

type QuestionCfg = {
    id: string,
    group: number,
    characterId: number,
    content: string,
    opts: string[],
    answer: number,
}

type BlackHoleEquipLevelCfg = {
    id: number,
    equipLv: number,
    buyCost: Condition[]
}

type NpcCfg = {
    id: number,
    name: string,
    spine: string,
    icon: string,
    planet: number
    taskDialog: number
}

type LightTablePuzzleCfg = {
    grid: LightTablePuzzleGridCfg[]
}

type LightTablePuzzleGridCfg = {
    id: number,
    type: number,
    dir: number,
}

type PlanetProfileReward = {
    id: string
    planetId: number
    area: number
    reward: { type: number, id: number, num: number, group: number }[]
    grouped: { [step: number]: Condition[] }
}

type ProfileBranchLevelQuestionCfg = {
    profile: number,
    exclusive: number,
    common: number,
}

type ProfileBranchLevelCfg = {
    id: number
    questions: ProfileBranchLevelQuestionCfg[]
    request: Condition[]
}

type TrainDailyTaskItemCfg = {
    id: number
    name: string
    desc: string
    icon: string
}

type TrainDailyTaskLevelCfg = {
    id: string
    costTime: number
}


type DailyTaskDialogCfg = {
    id: number,
    group: number,
    lang: string,
}

type BurstTaskItemCfg = {
    id: number
    name: string
    desc: string
    icon: string
    costTime: number
}

type TrainTechCfg = {
    id: number
    pre?: string[]
    name: string
    desc: string
    icon: string
    row: number
    ceil: number
    tmpId: string
}

type TrainTechLevelCfg = {
    id: string
    techId: string
    cost: Condition[]
    effect: { type: number, num: number, target?: number }
}

type PublicityPlayCfg = {
    id: string
    planetId: number
    lv: number
    reward: Condition
    req: number
    rate: number
    cost: number
    min: number
    max: number
}

type PrivilegeCardCfg = {
    id: string
    productId: string
    sort: number
    rmb: number
    monthly: number
    monthlyFirst: number
    quarterly: number
    quarterlyFirst: number
    once: Condition[]
    daily: Condition[]
}

export {
    ItemInfo,
    MapJsonItem,
    DebugInfo,
    AssetRef,
    BedData,
    ItemCfg,
    ChestCfg,
    BuildCfg,
    BuildUseInfo,
    CarriageThemeCfg,
    CharacterCfg,
    CharacterSkinCfg,
    CharacterFaceCfg,
    CharacterFragCfg,
    GalaxyCfg,
    PlanetCfg,
    PlanetMineCfg,
    ChapterPlanetMineCfg,
    PlanetMonsterCfg,
    PlanetMonsterLevelCfg,
    ChapterPlanetMonsterCfg,
    ChapterControlCfg,
    TrainCfg,
    TrainWorkCfg,
    TrainGoodsCfg,
    TrainGoodsLevelCfg,
    ArrestCfg,
    TrainGoodsLevelEffect,
    MoneyAreaInfo,
    MessageBoxOpts,
    TipsOpts,
    TypeIds,
    Trigger,
    UnlockFuncTarget,
    TaskTarget,
    Condition,
    UseBuildCfg,
    ExtractHistory,
    Reward,
    Guide,
    GuideStep,
    FingerGuide,
    WeakGuideCfg,
    AreaInfo,
    IdTargets,
    TaskCfg,
    AchievementCfg,
    AchievementTypeCfg,
    CharacterStarLvCfg,
    CharacterTalentCfg,
    BaseSkillCfg,
    BattleSkillCfg,
    BattleSkillPassiveCfg,
    CharacterLifeSkillCfg,
    CharacterExplorationSkillCfg,
    PlotStep,
    CharacterPlotStep,
    CharacterPlotControl,
    CharacterProfileCfg,
    CharacterProfileAttrCfg,
    SpineAnimCfg,
    ToolTabelCfg,
    ToolCfg,
    ToolLevelCfg,
    ExploreBoxCfg,
    EntrustLifeAttribute,
    EntrustLifeCfg,
    CarriageUsePosInfo,
    AccCarriageInfo,
    BuildLevelCfg,
    GoGameCfg,
    XYData,
    CurMaxData,
    QteGamePlayCfg,
    JumpTipJump,
    JumpTipsCfg,
    JackpotCfg,
    ShopCfg,
    FocusCfg,
    BlackHoleLayerCfg,
    TowerMonsterCfg,
    UnlockFuncCfg,
    EquipCfg,
    EquipLevelCfg,
    InstanceCfg,
    InstanceLevelCfg,
    WantedCfg,
    CharacterLevelCostCfg,
    BattleSkillViewCfg,
    DosingCondition,
    FieldSeedCfg,
    TransportLevelUpCfg,
    TransportLevelCfg,
    FieldLevelCfg,
    OreItemCfg,
    EquipMakeCfg,
    OreLevel,
    EquipEffectCfg,
    EquipStoreCfg,
    CollectMapCfg,
    PlanetAreaCfg,
    DailyTaskCfg,
    SpaceStoneCfg,
    QuestionCfg,
    BlackHoleEquipLevelCfg,
    TalentAttrCfg,
    NpcCfg,
    DialogOption,
    LightTablePuzzleGridCfg,
    LightTablePuzzleCfg,
    PlanetProfileCfg,
    BattleSkillTriggerCfg,
    BattleSkillObjectCfg,
    BattleSkillEffectCfg,
    BattleSkillEffectBuffCfg,
    PlanetProfileReward,
    ProfileBranchLevelCfg,
    ProfileBranchLevelQuestionCfg,
    TrainDailyTaskItemCfg,
    DailyTaskDialogCfg,
    ChapterPlanetSpCfg,
    TrainDailyTaskLevelCfg,
    BurstTaskItemCfg,
    TrainTechCfg,
    TrainTechLevelCfg,
    PublicityPlayCfg,
    PrivilegeCardCfg
}
