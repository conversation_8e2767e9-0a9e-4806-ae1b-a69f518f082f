import NodePool from "../../../../core/utils/NodePool";
import { MAX_ZINDEX } from "../../../common/constant/Constant";
import { HeroAction, HeroAnimation, PassengerAnimation, PassengerLifeAnimation, PassengerSpAnimation, PlanetMineType } from "../../../common/constant/Enums";
import EventType from "../../../common/event/EventType";
import { animHelper } from "../../../common/helper/AnimHelper";
import { gameHelper } from "../../../common/helper/GameHelper";
import { resHelper } from "../../../common/helper/ResHelper";
import { viewHelper } from "../../../common/helper/ViewHelper";
import HeroModel from "../../../model/hero/HeroModel";
import PlanetMineModel from "../../../model/planet/PlanetMineModel";
import Tool from "../../../model/tool/Tool";

const { ccclass, property } = cc._decorator;

@ccclass
export default class HeroCmpt extends mc.BaseCmptCtrl {

    private model: HeroModel = null

    @property(sp.Skeleton)
    private body: sp.Skeleton = null

    @property(sp.Skeleton)
    private body2: sp.Skeleton = null

    @property(sp.Skeleton)
    private tool: sp.Skeleton = null

    @property(cc.Node)
    private effect: cc.Node = null

    @property(cc.Node)
    private runYanwu: cc.Node = null

    private prePos: cc.Vec2 = null
    private action: HeroAction = null

    public autoZIndex: boolean = true

    private lastComboTime: number = 0;
    private collectStartSpeed: number = 1;
    private collectSlowTime: number = 0;
    private collectEndSpeed: number;
    private collectAnim: string = null //当前采集动画
    private collectAnim2: string = null //body2用
    private collectOrgAnim: string = null //当前原速采集动画
    private collectIdleAnim: string = null //当前采集待机动画
    private collectEffect: string = null //采集特效

    private flip: boolean = false

    private nodePool: NodePool = new NodePool()

    public listenEventMaps() {
        return [
            { [EventType.COLLECT_COMBO]: this.onCollectCombo },
            { [EventType.CHANGE_TOOL]: this.onChangeTool },
            { [EventType.HERO_QTE]: this.onQTE },
            { [EventType.HERO_RAGE_MODE_COLLECT]: this.playRageModeEffect },
        ]
    }

    public init(role: string) {
        this.model = gameHelper.hero
        this.collectStartSpeed = 1
        this.collectSlowTime = 0
        this.model.start()
        this.initView(role)
        this.initListener()
    }

    public onRemove() {
        this.unscheduleAllCallbacks()
    }

    private initView(role: string) {
        this.prePos = null
        this.node.opacity = 255
        this.showYanwu()
        this.updateDir()
        this.updatePosition()
        if (!role) {
            resHelper.loadOwnRoleSp(this.model.roleId, this.body, this.getTag())
            resHelper.loadOwnRoleSp(this.model.roleId, this.body2, this.getTag())
        }
        else {
            resHelper.loadJumpRoleSkin(role, this.body, this.getTag())
            resHelper.loadJumpRoleSkin(role, this.body2, this.getTag())
        }
        let fastPickTree = this.getCollectFastAnim(HeroAnimation.PICK_TREE)
        let fastPickOre = this.getCollectFastAnim(HeroAnimation.PICK_ORE)
        let mix = (body) => {
            body.mix2(PassengerLifeAnimation.IDLE, PassengerLifeAnimation.WALK, 0.15)
            body.mix2(PassengerLifeAnimation.IDLE, HeroAnimation.PICK_TREE, 0.15)
            body.mix2(PassengerLifeAnimation.IDLE, HeroAnimation.PICK_ORE, 0.15)
            body.mix2(PassengerLifeAnimation.IDLE, fastPickTree, 0.15)
            body.mix2(PassengerLifeAnimation.IDLE, fastPickOre, 0.15)
            body.mix2(PassengerLifeAnimation.WALK, HeroAnimation.PICK_TREE, 0.15)
            body.mix2(PassengerLifeAnimation.WALK, HeroAnimation.PICK_ORE, 0.15)
            body.mix2(HeroAnimation.PICK_TREE, fastPickTree, 0.15)
            body.mix2(HeroAnimation.PICK_ORE, fastPickOre, 0.15)
        }

        mix(this.body)
        mix(this.body2)
    }

    private initListener() {
        let toIdle = async (isEnd) => {
            this.resetCollectCombo()
            if (isEnd) {
                this.playAnimation(PassengerLifeAnimation.IDLE, true)
            }
            else {
                this.playAnimation(this.collectIdleAnim, true)
            }
        }

        this.body.setCompleteListener(async ({ animation }) => {
            if (this.model.isCollect() && (animation.name == this.collectAnim)) {
                let mine = this.model.getCollectTarget() as PlanetMineModel
                this.model.deCombo()
                if (this.model.getCombo() <= 0) this.lastComboTime = 0

                eventCenter.emit(EventType.HERO_PICK_ANIM_END)

                if (!mine.dead) {
                    // twlog.info("collectEnd", this.collectCount, this.model.collectCombo)
                    let nextAnim = animation.name
                    let fastAnim = this.getCollectFastAnim(this.collectOrgAnim)
                    if (fastAnim == animation.name) { //如果处于快速状态
                        if (!this.isFastCollect()) { //当前速度小于快速，恢复成普通模式
                            nextAnim = this.collectOrgAnim
                        }
                    }
                    else {
                        if (this.isFastCollect()) { //反之，升级成快速模式
                            nextAnim = fastAnim
                        }
                    }

                    if (nextAnim != animation.name) {
                        let speed = this.getCollectSpeed()
                        let fromDur = this.getAnimationDuration(animation.name)
                        let toDur = this.getAnimationDuration(nextAnim)
                        this.collectStartSpeed = toDur / fromDur * speed //因为两个动画的长度不一样，这里对速度做一下等比转换
                        this.calcCollectEnd(speed, this.model.getCombo(), toDur)
                        this.setBodyTimeScale(this.collectStartSpeed)
                        this.collectAnim = nextAnim
                    }

                    if (this.model.isCollectAct()) {
                        this._playAnimation(nextAnim, false)
                    }
                    else {
                        toIdle(false)
                    }
                }
                else {
                    toIdle(true)

                    //直接切状态太生硬，这里延时过度一下
                    ut.wait(0.1, this).then(() => {
                        this.model.collectEnd()
                    })
                }
            }
        })

        this.body.setEventListener(({ animation }, { data }) => {
            if (animation.name == this.collectAnim) {
                let mine = this.model.getCollectTarget()
                if (data.name == "effect") {
                    if (mine.type == PlanetMineType.TREE || mine.type == PlanetMineType.PART || mine.type == PlanetMineType.SEED) {
                        this.playCollectEffect()
                    }
                }
                else if (data.name == 'pick') {
                    if (mine.type == PlanetMineType.ORE) {
                        this.playCollectEffect()
                    }

                    this.model.collect(mine)
                }
            }
        })
    }

    update(dt: number) {
        if (!this.model) return
        this.setBodyTimeScale(1)
        this.updateAction()
        this.updatePosition()
        this.updateMoveSpeed()
        if (this.body.animation == this.collectAnim) {
            this.updateCollectSpeed()
        }
        this.updateDir()
    }

    private updateMoveSpeed() {
        if (this.body.animation == PassengerLifeAnimation.WALK) {
            let moveSpeed = this.model.getMoveSpeed()
            this.setBodyTimeScale(moveSpeed / this.model.orgMoveSpeed)
        }
    }

    private onEnterCollect() {
        let mine = this.model.getCollectTarget()

        this.autoZIndex = false
        this.node.zIndex = mine.getZIndex() + 1

        this.updateCollectAnim()
        this.updateCollectSpeed()
        if (mine.type == PlanetMineType.SEED) {
            this.body2.node.active = true
        }
        else {
            this.body2.node.active = false
        }
        this._playAnimation(this.collectIdleAnim, true)
        this.showTool(true)
    }

    private updateCollectAnim() {
        this.collectIdleAnim = PassengerLifeAnimation.IDLE
        let tool = this.model.tool
        let animType = tool?.anim
        if (animType == PlanetMineType.PART) {
            this.collectAnim = HeroAnimation.PICK_PART
        }
        else if (animType == PlanetMineType.TREE) {
            this.collectAnim = HeroAnimation.PICK_TREE
        }
        else if (animType == PlanetMineType.ORE) {
            this.collectAnim = HeroAnimation.PICK_ORE
            this.collectEffect = "caikuang"
        }
        else if (animType == PlanetMineType.SEED) {
            this.collectAnim = HeroAnimation.PICK_SEED_DOWN
            this.collectIdleAnim = HeroAnimation.PICK_SEED_IDLE_DOWN
        }
        else {
            this.collectAnim = HeroAnimation.PICK_PART
        }
        this.collectOrgAnim = this.collectAnim
    }

    private resetCollectCombo() {
        this.model.resetCombo()
        this.collectStartSpeed = 1
        this.collectSlowTime = 0
        this.lastComboTime = 0
        this.collectEndSpeed = 0
        this.updateCollectSpeed()
    }

    private onEnxitCollect() {
        this.autoZIndex = true
        this.resetCollectCombo()
        this.setBodyTimeScale(1)
        this.collectAnim = null
        this.collectOrgAnim = null
        this.collectIdleAnim = null
        this.collectEffect = null
        this.body2.node.active = false
        this.showTool(false)
    }

    private getCollectFastAnim(anim: string) {
        let replace = anim
        if (anim.includes("aniPickSeed")) {
            replace = "aniPickSeed"
        }
        return anim.replace(replace, replace + "2") as HeroAnimation
    }

    private onCollectCombo(combo, orgCombo) {
        if (combo > 1) {
            if (!this.collectAnim) {
                this.updateCollectAnim()
            }
            let dur = this.getAnimationDuration(this.collectAnim)
            let cur = 0
            if (this.body.animation == this.collectAnim) {
                cur = this.getCurrentTrackTime()
            }
            let dt = dur
            if (this.lastComboTime) {
                dt = Math.min(dur, (gameHelper.now() - this.lastComboTime) / ut.Time.Second)
            }
            let preDt = dur / this.collectStartSpeed
            if (preDt < dt) { //如果前一次按的速度比现在按的快，说明速度已经开始衰减，取当前的速度即可
                this.collectStartSpeed = this.getCollectSpeed()
            }
            else { //如果按得比前一次快，重新计算一下初速度
                this.collectStartSpeed = dur / dt
            }
            this.calcCollectEnd(this.collectStartSpeed, combo, dur, cur)
        }
        this.lastComboTime = gameHelper.now()
    }

    private calcCollectEnd(startSpeed, combo, dur, cur = 0) {
        let s = (combo * dur - cur) //总路程
        s -= dur //减掉降速之前的距离
        if (s <= 0) { //没有降速阶段了
            this.collectSlowTime = 0
        }
        else {
            let minTime = s / startSpeed
            let maxTime = 2 * s / (startSpeed + 1)
            this.collectSlowTime = cc.misc.clampf(0.5, minTime, maxTime) //从速度下降到结束花的时间
            this.collectEndSpeed = 2 * s / this.collectSlowTime - startSpeed
        }
    }

    private getCollectSpeed() {
        let orgSpeed = this.getOrgAtkSpeed()

        if (!this.lastComboTime || !this.collectEndSpeed) return orgSpeed //还没有开始连击的时候

        if (this.model.getCombo() <= 0) return this.collectStartSpeed

        if (this.collectSlowTime) { //有降速阶段
            let passTime = (gameHelper.now() - this.lastComboTime) / ut.Time.Second
            let dur = this.getAnimationDuration(this.collectAnim)
            let dt = dur / this.collectStartSpeed
            let slowTime = passTime - dt
            if (slowTime > 0) { //如果passTime超过了上次点击的间隔，开始线性衰减
                return cc.misc.lerp(this.collectStartSpeed, this.collectEndSpeed, cc.misc.clamp01(slowTime / this.collectSlowTime))
            }
            else {
                return this.collectStartSpeed
            }
        }
        else {
            return this.collectStartSpeed
        }
    }

    private getOrgAtkSpeed() {
        return 1
    }

    private isFastCollect() {
        let speed = this.getCollectSpeed()
        if (speed > 1) {
            return true
        }
        return false
    }

    public getAnimationDuration(animation) {
        let dur = this.body.getAnimationDuration(animation)
        return dur
    }

    private getCurrentTrackTime() {
        let cur = this.body.getCurrent(0).trackTime
        return cur
    }

    private updateCollectSpeed(dt = 0) {
        let orgSpeed = this.getOrgAtkSpeed()
        if (this.model.getCombo() <= 0) {
            if (this.collectStartSpeed && this.collectStartSpeed > orgSpeed) {
                this.collectStartSpeed = cc.misc.lerp(this.collectStartSpeed, orgSpeed, cc.misc.clamp01(dt * 5))
            }
        }
        let speed = this.getCollectSpeed()
        this.setBodyTimeScale(speed)
    }

    private updateAction() {
        let newAction = this.model.getAction()
        if (this.action == HeroAction.COLLECT && this.body.animation == this.collectIdleAnim && this.model.isCollectAct()) {
            this.updateCollectAnim()
            this._playAnimation(this.collectAnim, false)
        }
        if (this.action == newAction) return
        let cfg: any = [
            {
                action: HeroAction.IDLE, enter: () => {
                    this.playAnimation(PassengerLifeAnimation.IDLE, true)
                }
            },
            {
                action: HeroAction.MOVE, enter: () => {
                    this.playAnimation(this.model.anim || PassengerLifeAnimation.WALK, true)
                }
            },
            {
                action: HeroAction.COLLECT, enter: this.onEnterCollect, exit: this.onEnxitCollect
            },
            {
                action: HeroAction.SAVE_CAT, enter: this.onSaveCatStart,
            },
            {
                action: HeroAction.CHASE_1, enter: () => { this.playAnimation(PassengerSpAnimation.TALK_SHOUT) }
            },
            {
                action: HeroAction.CHASE_2, enter: () => { this.showYanwu(true) }, exit: () => { this.showYanwu() }
            },
            {
                action: HeroAction.CHASE_3, enter: async () => {
                    await this.playAnimation(PassengerSpAnimation.TALK_THINK)
                    this.playAnimation(PassengerLifeAnimation.IDLE, true)
                }
            },
            {
                action: HeroAction.WAIT_TO_RAGE_MODE, enter: () => {
                    this.playAnimation(HeroAnimation.RAGE_MODE_2, true)
                }
            },
            {
                action: HeroAction.SKI_JUMP, enter: () => {
                    this.playAnimation(HeroAnimation.SKI_JUMP, false)
                }
            },
            {
                action: HeroAction.SKI_ING, enter: () => {
                    this.playAnimation(HeroAnimation.SKI_ING, true)
                }
            }
        ]

        let enterAction = cfg.find(m => m.action == newAction)
        let exitAction = cfg.find(m => m.action == this.action)

        if (exitAction && exitAction.exit) {
            exitAction.exit.call(this)
        }

        if (enterAction && enterAction.enter) {
            enterAction.enter.call(this)
        }
        else {
            this.playAnimation(PassengerLifeAnimation.IDLE, true)
        }

        this.action = newAction
    }

    public playAnimation(name: string, loop?: boolean) {
        if (this.body.animation == name) return
        return this._playAnimation(name, loop)
    }

    private _playAnimation(name: string, loop?: boolean) {
        if (this.body2.node.active) {
            let anim = name
            if (name == this.collectAnim) {
                if (name == this.collectOrgAnim) {
                    anim = HeroAnimation.PICK_SEED_UP
                }
                else {
                    anim = this.getCollectFastAnim(HeroAnimation.PICK_SEED_UP)
                }
            }
            else if (name == this.collectIdleAnim) {
                anim = HeroAnimation.PICK_SEED_IDLE_UP
            }
            this.body2.playAnimation(anim, loop)
        }
        if (this.tool.node.active) {
            if (name == this.collectAnim) {
                let anim = "aniPickSeed"
                if (name != this.collectOrgAnim) {
                    anim = this.getCollectFastAnim(anim)
                }
                this.tool.playAnimation(anim)
            }
            else if (name == this.collectIdleAnim) {
                this.tool.playAnimation("idle", true)
            }
        }
        return this.body.playAnimation(name, loop)
    }

    private setBodyTimeScale(scale) {
        this.body.timeScale = scale
        this.body2.timeScale = scale
    }

    private updatePosition() {
        let pos = this.model.getPosition()
        if (this.prePos) {
            if (pos.equals(this.prePos)) return
            this.prePos.set(pos)
        } else {
            this.prePos = pos.clone()
        }
        this.node.setPosition(pos)
        this.updateZIndex()
    }

    private updateDir() {
        let flip = this.model.getFlip()
        if (this.flip != flip) {
            this.flip = flip
            this.setFlip(flip)
        }
    }

    private setFlip(left) {
        let scale = Math.abs(this.body.node.scaleX)
        this.body.node.scaleX = left ? -scale : scale
        this.body2.node.scaleX = left ? -scale : scale
    }

    private updateZIndex() {
        if (this.autoZIndex) {
            this.node.zIndex = MAX_ZINDEX - this.node.y
        }
    }

    public onClean(): void {
        super.onClean()
        gameHelper.hero.reset()
    }

    private showTool(show: boolean, tool?: Tool) {
        if (show) {
            tool = tool || this.model.tool

            if (tool?.anim == PlanetMineType.SEED) {
                this.tool.node.active = true
                this.tool.setSkin(tool.skin)
            }
            else {
                let skin = tool?.skin
                if (gameHelper.tool.isBless()) {
                    skin = `skin_tool_sp_${tool.getType()}`
                }
                resHelper.switchTool(this.body, tool?.anim, skin, this.getTag())
                this.tool.node.active = false
            }
        }
        else {
            this.tool.node.active = false
            resHelper.hideTool(this.body)
        }
    }

    private onChangeTool(tool) {
        this.showTool(true, tool)
    }

    private playCollectEffect() {
        if (!this.collectEffect) return
        let template = this.effect.Child(this.collectEffect)
        if (!template) return
        let node = this.nodePool.get(template, this.effect)
        node.name = `${this.collectEffect}_Copy`
        node.Component(sp.Skeleton).playAnimation(this.collectEffect).then(() => {
            this.nodePool.put(template, node)
        })
    }

    private playRageModeEffect() {
        let template = this.effect.Child("rageMode")
        if (!template) return
        let node = this.nodePool.get(template, this.effect)
        node.name = "rageMode_Copy"
        node.Component(sp.Skeleton).playAnimation("enter")
        viewHelper.shake(this.node.parent.parent, 0.2)
    }

    private onSaveCatStart() {
        let anim = "sp/aniSave1"
        if (!this.body.findAnimation(anim)) {
            anim = PassengerLifeAnimation.IDLE
        }
        this._playAnimation(anim)
    }

    private async onQTE(damageMul: number) {
        if (damageMul <= 0) {
            let orgAnim = this.body.animation
            let loop = this.body.loop
            let mine = this.model.getTargetModel()
            let anim
            if (mine.type == PlanetMineType.PART) {
                anim = HeroAnimation.QTE_PART_MISS
            }
            else if (mine.type == PlanetMineType.TREE) {
                anim = HeroAnimation.QTE_TREE_MISS
            }
            else if (mine.type == PlanetMineType.ORE) {
                anim = HeroAnimation.QTE_ORE_MISS
            }
            await this._playAnimation(anim)
            eventCenter.emit(EventType.HERO_PICK_ANIM_END)

            this._playAnimation(orgAnim, loop)

            //showMiss
        }

    }

    private showYanwu(bol: boolean = false) {
        if (!this.runYanwu) return
        this.runYanwu.active = bol
        if (bol) {
            this.playAnimation(HeroAnimation.RUN, true)
            this.runYanwu.Component(cc.ParticleSystem).resetSystem()
        } else {
            this.runYanwu.Component(cc.ParticleSystem).stopSystem()
        }
    }
}