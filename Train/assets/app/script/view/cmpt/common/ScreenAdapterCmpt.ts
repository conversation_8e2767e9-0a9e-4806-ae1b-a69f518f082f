const { ccclass, property, menu } = cc._decorator;

enum AdapterType {
    maxScale = 0,
    minScale,
    maxScaleY,
}

@ccclass
export default class ScreenAdapterCmpt extends cc.Component {

    @property({
        type: cc.Enum(AdapterType)
    })
    adapterType: AdapterType = AdapterType.maxScale;

    @property({
        visible() { return this.adapterType == AdapterType.minScale },
    })
    actionFlag: Boolean = false;

    onEnable() {

        switch (this.adapterType) {

            case AdapterType.minScale:
                this.minScale()
                if (this.actionFlag) {
                    this.playAction();
                }
                break;

            case AdapterType.maxScale:
                this.maxScale()
                break;
            case AdapterType.maxScaleY:
                this.maxScaleY()
                break;
            default:
                break;
        }
    }

    private maxScale() {
        let { width, height } = this.node.getContentSize()
        let sx = cc.winSize.width / width;
        let sy = cc.winSize.height / height;
        let scale = Math.max(sx, sy)
        if (scale > 1) {
            this.node.scale = scale
        }
    }

    private maxScaleY() {
        let { height } = this.node.getContentSize()
        this.node.scaleY = cc.winSize.height / height
    }

    private minScale() {
        let { width, height } = cc.view.getDesignResolutionSize()
        let sx = cc.winSize.width / width;
        let sy = cc.winSize.height / height;
        this.node.scale = Math.min(sx, sy);
    }

    private playAction() {

        let originalScale = this.node.scale;
        this.node.scale = 0.2 * originalScale;
        cc.tween(this.node)
            .to(0.2, { scale: 1.05 * originalScale })
            .delay(0.05)
            .to(0.1, { scale: originalScale })
            .start();
    }
}
