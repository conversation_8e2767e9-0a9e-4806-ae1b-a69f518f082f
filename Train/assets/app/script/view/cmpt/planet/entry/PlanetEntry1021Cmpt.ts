import { Hero<PERSON>ni<PERSON>, LangCfgName, MarkNewType, NPC_ID, PassengerLifeAnimation, UIFunctionType } from "../../../../common/constant/Enums";
import EventType from "../../../../common/event/EventType";
import { unlockHelper } from "../../../../common/helper/UnlockHelper";
import { viewHelper } from "../../../../common/helper/ViewHelper";
import PlanetModel from "../../../../model/planet/PlanetModel";
import MountPointCmpt from "../../common/MountPointCmpt";
import RoleSpeakCmpt from "../../role/RoleSpeakCmpt";
import PlanetPlayNameCmpt from "../PlanetPlayNameCmpt";
import PlanetEntryCmpt from "./PlanetEntryCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class PlanetEntry1021Cmpt extends PlanetEntryCmpt {

    public listenEventMaps() {
        return [
            { [EventType.GUIDE_UNLOCK_FUNTION]: this.onFunctionUnlock },
        ]
    }


    protected initView() {
        this.initBranch()

    }

    private onFunctionUnlock(type: UIFunctionType) {
    }

    private initBranch() {
        let type = UIFunctionType.PLAY_TIME_STONE
        let game = this.Component(MountPointCmpt).getPoint("branch")
        game.Child("play_name", PlanetPlayNameCmpt).init(this.model, type)
        game.Child("evt").on("click", () => {
            viewHelper.gotoPlanetBranch(this.model)
        })
        this.setLockColor(game, type)
    }
}