import EventType from "../../../common/event/EventType";
import { gameHelper } from "../../../common/helper/GameHelper";
import PlanetEmptyNode from "../../../model/planet/PlanetEmptyNode";
import PlanetWindCtrl from "../../planet/PlanetWindCtrl";
import PlanetNodeCmpt from "./PlanetNodeCmpt";

const { ccclass, property } = cc._decorator;

@ccclass
export default class JumpGame3Cmpt extends PlanetNodeCmpt {
    model: PlanetEmptyNode = null
    planetCtrl: PlanetWindCtrl = null
    heroNode: cc.Node = null

    powerSp: cc.Sprite = null
    points: cc.Node[] = []

    get curIndex() { return this.model.progress }
    set curIndex(val: number) { this.model.progress = val }

    public listenEventMaps() {
        return [
            { [EventType.TARGET_PLANET_NODE]: this.onTarget },
        ]
    }

    public async init(model: PlanetEmptyNode, planetCtrl: PlanetWindCtrl) {
        super.init(model)
        this.planetCtrl = planetCtrl
        this.heroNode = planetCtrl.getHeroNode()
        this.listenEventMaps()
        this.powerSp = this.heroNode.Child('ui/power/bar', cc.Sprite)
        this.points = this.node.children
        for (let i = this.curIndex + 1; i < this.points.length; i++) {
            this.points[i].active = false
        }
    }


    async onTarget(model: PlanetEmptyNode) {
        if (this.model != model) return
        this.planetCtrl.focusHero()
        const pos = this.getFudaoCenter(this.curIndex)
        this.heroNode.setPosition(pos)
        gameHelper.hero.setPosition(pos)
        await this.standOn()
    }

    async standOn() {

    }

    getFudaoCenter(index: number) {
        const point = this.points[index]
        const line = point.Child("line")
        const startPos = this.node.getPosition()
        const center = ut.convertToNodeAR(line, this.node)
        return cc.v2(startPos.x + center.x, startPos.y + center.y)
    }

}
